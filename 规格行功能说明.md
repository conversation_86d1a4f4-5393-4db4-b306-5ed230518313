# 用法用量规格行功能说明

## 功能概述
在用法用量部分的"每日x次,每次xg"这行的前面，新增了一行可以根据情况进行显示或者隐藏的"规格"行。

## 修改内容

### 1. BRPresUsageView.h 头文件修改
- 新增了 `specificationLabel` 属性：用于显示"规格:"标签
- 新增了 `isSpecificationRowVisible` 属性：控制规格行的显示状态
- 新增了控制方法：
  - `setSpecificationRowVisible:(BOOL)visible` - 设置规格行显示/隐藏
  - `isSpecificationRowVisible` - 获取规格行显示状态

### 2. BRPresUsageView.m 实现文件修改
- 在 `buildOtherTypeViewWithDrugForm:` 方法中添加了规格行的创建逻辑
- 规格行位置：在"每日x次"和"每次xg"之间
- 添加了 `setSpecificationRowVisible:` 方法实现，支持动态显示/隐藏
- 自动调整视图高度以适应规格行的显示/隐藏

### 3. PrescriptionViewController.m 使用示例
- 添加了使用示例注释，展示如何控制规格行的显示

## 功能特点

### 1. 动态显示/隐藏
- 默认状态：隐藏
- 可通过 `setSpecificationRowVisible:` 方法动态控制显示状态

### 2. 自动布局调整
- 显示规格行时，自动调整"每日x次，每次xg"行的位置
- 自动调整整个用法用量视图的高度（从100增加到130）
- 保持"每日x次，每次xg"在同一行显示，不改变原有布局
- 保持其他行的显示高度不受影响

### 3. 文字垂直居中
- 规格行文字在虚线分割线区域内垂直居中显示
- 与其他行保持一致的高度和样式

## 使用方法

### 基本用法
```objc
// 显示规格行
[usageView setSpecificationRowVisible:YES];

// 隐藏规格行
[usageView setSpecificationRowVisible:NO];

// 检查规格行是否可见
BOOL isVisible = [usageView isSpecificationRowVisible];
```

### 重要：调用顺序
规格行功能只在特定剂型下可用。正确的调用顺序是：

```objc
// 方法1：先设置可见性，再设置剂型
[usageView setSpecificationRowVisible:YES];
[usageView setDrugForm:@"膏方"]; // 或其他非颗粒/饮片/代煎/外用中药的剂型

// 方法2：先设置剂型，再设置可见性
[usageView setDrugForm:@"膏方"];
[usageView setSpecificationRowVisible:YES];
```

### 支持的剂型
规格行功能仅在以下剂型中可用（会调用 buildOtherTypeViewWithDrugForm 方法）：
- 膏方
- 胶囊
- 散剂
- 水丸
- 蜜丸
- 其他非颗粒/饮片/代煎/外用中药的剂型

### 不支持的剂型
以下剂型使用不同的布局，不支持规格行功能：
- 颗粒
- 饮片
- 代煎
- 外用中药

## 布局结构

### 支持规格行的剂型布局（膏方、胶囊、散剂等）
```
用法用量
├── 预计总重: xxx
├── ─────────────── (虚线分割线)
├── 规格 (可显示/隐藏的独立行)
├── ─────────────── (虚线分割线，仅在规格行可见时显示)
├── 每日 [x] 次，每次 [x] g (同一行显示)
├── 预计服用 [x] 天
└── ─────────────── (虚线分割线)
```

### 不支持规格行的剂型布局（颗粒、饮片、代煎、外用中药）
```
用法用量
├── 共 [x] 剂，每日 [x] 剂，每剂分 [x] 次服用 (单行显示)
└── ─────────────── (虚线分割线)
```

### 重要说明
- **规格行**：独立的一行，只显示"规格"文字，不显示内容
- **每日x次，每次xg**：保持在同一行显示，与原来的布局一致
- **规格行位置**：在"每日x次，每次xg"这一行的前面
- **剂型限制**：只有特定剂型支持规格行功能

## 注意事项
1. 规格行目前只显示"规格"文字，不显示具体内容
2. 规格行的显示/隐藏会自动调整整个用法用量区域的高度（100px ↔ 130px）
3. 该功能主要适用于其他剂型（非颗粒、饮片、代煎、外用中药）
4. 规格行与其他行保持相同的字体大小和颜色样式
5. "每日x次，每次xg"保持在同一行显示，不会被分开
6. 规格行是独立的一行，位于"每日x次，每次xg"行的前面
